#!/usr/bin/env python3
"""
CSV Test Generator - Extract financial SMS messages from CSV and create comprehensive tests.
"""

import csv
import re
import json
import asyncio
from typing import List, Dict, Any, Tuple
from collections import defaultdict, Counter
from sms_parser import SMSParser, parse_sms_sync


class CSVTestGenerator:
    """Generate comprehensive tests from CSV SMS data."""
    
    def __init__(self, csv_file: str):
        self.csv_file = csv_file
        self.financial_sms = []
        self.parser = SMSParser()
        
        # Financial keywords to identify relevant SMS
        self.financial_keywords = [
            'debited', 'credited', 'UPI', 'EMI', 'loan', 'salary', 'balance', 
            'payment', 'transaction', 'txn', 'refno', 'ref no', 'amount',
            'Rs', '₹', 'INR', 'A/c', 'account', 'card', 'debit', 'credit',
            'transfer', 'trf', 'NEFT', 'IMPS', 'RTGS', 'ATM', 'POS',
            'merchant', 'bill', 'recharge', 'topup', 'cashback', 'refund'
        ]
    
    def extract_financial_sms(self) -> List[Dict[str, Any]]:
        """Extract all financial SMS messages from CSV."""
        financial_messages = []
        
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                for row in reader:
                    sms_text = row.get('text', '').strip()
                    sender = row.get('senderAddress', '').strip()
                    timestamp = row.get('updateAt', '').strip()
                    
                    if self._is_financial_sms(sms_text):
                        financial_messages.append({
                            'text': sms_text,
                            'sender': sender,
                            'timestamp': timestamp,
                            'id': row.get('id', ''),
                            'phone': row.get('phoneNumber', '')
                        })
        
        except Exception as e:
            print(f"Error reading CSV file: {e}")
            return []
        
        self.financial_sms = financial_messages
        print(f"Extracted {len(financial_messages)} financial SMS messages")
        return financial_messages
    
    def _is_financial_sms(self, text: str) -> bool:
        """Check if SMS contains financial keywords and is not promotional/OTP."""
        text_lower = text.lower()

        # First, exclude non-financial messages
        non_financial_patterns = [
            r'otp.*(?:verification|verify|code|pin)',
            r'(?:offer|discount|sale|promo|free|win|congratulations)',
            r'(?:buy\s*1\s*get\s*1|b1g1|bogo)',
            r'(?:click|visit|shop now|grab now)',
            r'(?:unsubscribe|stop|help)',
            r'(?:booking.*confirmed|ticket)',
            r'(?:delivery.*today|out for delivery)',
            r'(?:voucher|coupon|cashback)',
            r'whatsapp\.com',
            r'https?://',
        ]

        for pattern in non_financial_patterns:
            if re.search(pattern, text_lower):
                return False

        # Check for financial keywords
        for keyword in self.financial_keywords:
            if keyword.lower() in text_lower:
                return True

        # Check for amount patterns
        amount_patterns = [
            r'rs\.?\s*\d+',
            r'₹\s*\d+',
            r'inr\s*\d+',
            r'\d+\.\d{2}',
        ]

        for pattern in amount_patterns:
            if re.search(pattern, text_lower):
                return True

        return False
    
    async def test_all_messages(self) -> Dict[str, Any]:
        """Test all financial SMS messages with the parser."""
        if not self.financial_sms:
            self.extract_financial_sms()
        
        results = {
            'total_messages': len(self.financial_sms),
            'successfully_parsed': 0,
            'failed_to_parse': 0,
            'parsing_results': [],
            'error_messages': [],
            'sender_analysis': defaultdict(int),
            'pattern_analysis': defaultdict(int)
        }
        
        print(f"Testing {len(self.financial_sms)} financial SMS messages...")
        
        for i, sms_data in enumerate(self.financial_sms):
            try:
                sms_text = sms_data['text']
                sender = sms_data['sender']
                
                # Parse the SMS
                parsed_results = await self.parser.parse_sms(sms_text)
                
                # Track sender statistics
                results['sender_analysis'][sender] += 1
                
                if parsed_results:
                    results['successfully_parsed'] += 1
                    
                    # Analyze patterns
                    for result in parsed_results:
                        sms_type = result.get('sms_type', 'Unknown')
                        subtype = result.get('sms_event_subtype', 'Unknown')
                        results['pattern_analysis'][f"{sms_type}:{subtype}"] += 1
                    
                    results['parsing_results'].append({
                        'index': i,
                        'sender': sender,
                        'original_text': sms_text[:100] + '...' if len(sms_text) > 100 else sms_text,
                        'parsed_count': len(parsed_results),
                        'results': parsed_results
                    })
                else:
                    results['failed_to_parse'] += 1
                    results['error_messages'].append({
                        'index': i,
                        'sender': sender,
                        'text': sms_text[:100] + '...' if len(sms_text) > 100 else sms_text,
                        'error': 'No structured data extracted'
                    })
            
            except Exception as e:
                results['failed_to_parse'] += 1
                results['error_messages'].append({
                    'index': i,
                    'sender': sms_data.get('sender', 'Unknown'),
                    'text': sms_data.get('text', '')[:100],
                    'error': str(e)
                })
            
            # Progress indicator
            if (i + 1) % 50 == 0:
                print(f"Processed {i + 1}/{len(self.financial_sms)} messages...")
        
        return results
    
    def analyze_patterns(self) -> Dict[str, Any]:
        """Analyze patterns in the financial SMS messages to improve regex."""
        if not self.financial_sms:
            self.extract_financial_sms()
        
        analysis = {
            'amount_patterns': [],
            'date_patterns': [],
            'account_patterns': [],
            'reference_patterns': [],
            'sender_patterns': defaultdict(list),
            'common_phrases': Counter()
        }
        
        for sms_data in self.financial_sms:
            text = sms_data['text']
            sender = sms_data['sender']
            
            # Extract amount patterns
            amount_matches = re.findall(r'(?:Rs\.?|₹|INR)\s*[\d,]+(?:\.\d{1,2})?', text, re.IGNORECASE)
            analysis['amount_patterns'].extend(amount_matches)
            
            # Extract date patterns
            date_matches = re.findall(r'\d{1,2}[A-Za-z]{3}\d{2,4}|\d{1,2}[-/]\d{1,2}[-/]\d{2,4}', text)
            analysis['date_patterns'].extend(date_matches)
            
            # Extract account patterns
            account_matches = re.findall(r'A/c\s*[X\*]*\d{3,6}|Account\s*[X\*]*\d{3,6}', text, re.IGNORECASE)
            analysis['account_patterns'].extend(account_matches)
            
            # Extract reference patterns
            ref_matches = re.findall(r'Ref(?:no|erence)?\s*:?\s*\w+', text, re.IGNORECASE)
            analysis['reference_patterns'].extend(ref_matches)
            
            # Group by sender
            analysis['sender_patterns'][sender].append(text[:100])
            
            # Extract common phrases
            words = re.findall(r'\b\w+\b', text.lower())
            for word in words:
                if len(word) > 3:  # Skip short words
                    analysis['common_phrases'][word] += 1
        
        return analysis
    
    def generate_test_cases(self, max_cases: int = 100) -> List[Dict[str, Any]]:
        """Generate test cases from the most diverse SMS patterns."""
        if not self.financial_sms:
            self.extract_financial_sms()
        
        # Group by sender to get diverse examples
        sender_groups = defaultdict(list)
        for sms in self.financial_sms:
            sender_groups[sms['sender']].append(sms)
        
        test_cases = []
        cases_per_sender = max(1, max_cases // len(sender_groups))
        
        for sender, messages in sender_groups.items():
            # Take up to cases_per_sender from each sender
            selected = messages[:cases_per_sender]
            for msg in selected:
                test_cases.append({
                    'description': f"SMS from {sender}",
                    'input': msg['text'],
                    'sender': sender,
                    'timestamp': msg['timestamp'],
                    'expected_fields': self._extract_expected_fields(msg['text'])
                })
                
                if len(test_cases) >= max_cases:
                    break
            
            if len(test_cases) >= max_cases:
                break
        
        return test_cases[:max_cases]
    
    def _extract_expected_fields(self, text: str) -> Dict[str, Any]:
        """Extract expected fields from SMS text for validation."""
        expected = {}
        
        # Extract amount
        amount_match = re.search(r'(?:Rs\.?|₹|INR)\s*([\d,]+(?:\.\d{1,2})?)', text, re.IGNORECASE)
        if amount_match:
            expected['amount'] = amount_match.group(1).replace(',', '')
        
        # Extract account number
        account_match = re.search(r'A/c\s*([X\*]*\d{3,6})', text, re.IGNORECASE)
        if account_match:
            expected['account_number'] = account_match.group(1)
        
        # Extract reference number
        ref_match = re.search(r'Ref(?:no|erence)?\s*:?\s*(\w+)', text, re.IGNORECASE)
        if ref_match:
            expected['txn_ref'] = ref_match.group(1)
        
        # Extract date
        date_match = re.search(r'(\d{1,2}[A-Za-z]{3}\d{2,4})', text)
        if date_match:
            expected['date'] = date_match.group(1)
        
        # Determine transaction type
        if 'debited' in text.lower():
            expected['sms_info_type'] = 'Outflow'
        elif 'credited' in text.lower():
            expected['sms_info_type'] = 'Inflow'
        
        if 'UPI' in text.upper():
            expected['sms_event_subtype'] = 'UPI'
        elif 'card' in text.lower():
            expected['sms_event_subtype'] = 'Debit Card'
        
        return expected


async def main():
    """Main function to run CSV analysis and testing."""
    generator = CSVTestGenerator('sms_backup.csv')
    
    print("🔍 Extracting financial SMS messages from CSV...")
    financial_messages = generator.extract_financial_sms()
    
    print(f"\n📊 Found {len(financial_messages)} financial SMS messages")
    
    # Show sender distribution
    sender_counts = Counter(msg['sender'] for msg in financial_messages)
    print("\n📱 Top senders:")
    for sender, count in sender_counts.most_common(10):
        print(f"  {sender}: {count} messages")
    
    print("\n🧪 Testing all messages with SMS parser...")
    test_results = await generator.test_all_messages()
    
    print(f"\n📈 Test Results Summary:")
    print(f"  Total messages: {test_results['total_messages']}")
    print(f"  Successfully parsed: {test_results['successfully_parsed']}")
    print(f"  Failed to parse: {test_results['failed_to_parse']}")
    print(f"  Success rate: {test_results['successfully_parsed']/test_results['total_messages']*100:.1f}%")
    
    print(f"\n🏷️ Pattern Analysis:")
    for pattern, count in Counter(test_results['pattern_analysis']).most_common(10):
        print(f"  {pattern}: {count}")
    
    # Save detailed results
    with open('csv_test_results.json', 'w') as f:
        json.dump(test_results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to csv_test_results.json")
    
    # Generate test cases
    print(f"\n🎯 Generating test cases...")
    test_cases = generator.generate_test_cases(50)
    
    with open('generated_test_cases.json', 'w') as f:
        json.dump(test_cases, f, indent=2)
    
    print(f"Generated {len(test_cases)} test cases saved to generated_test_cases.json")
    
    # Pattern analysis
    print(f"\n🔍 Analyzing patterns for regex improvement...")
    pattern_analysis = generator.analyze_patterns()
    
    with open('pattern_analysis.json', 'w') as f:
        json.dump({
            'amount_patterns': list(set(pattern_analysis['amount_patterns']))[:20],
            'date_patterns': list(set(pattern_analysis['date_patterns']))[:20],
            'account_patterns': list(set(pattern_analysis['account_patterns']))[:20],
            'reference_patterns': list(set(pattern_analysis['reference_patterns']))[:20],
            'common_phrases': dict(pattern_analysis['common_phrases'].most_common(50))
        }, f, indent=2)
    
    print(f"Pattern analysis saved to pattern_analysis.json")


if __name__ == "__main__":
    asyncio.run(main())
