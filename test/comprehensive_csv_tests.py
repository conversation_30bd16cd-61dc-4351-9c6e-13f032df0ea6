#!/usr/bin/env python3
"""
Comprehensive test suite using real SMS data from CSV file.
Tests the improved SMS parser with actual financial SMS messages.
"""

import asyncio
import json
import csv
from typing import List, Dict, Any
from collections import Counter, defaultdict
from sms_parser import SMSParser, parse_sms_sync


class ComprehensiveCSVTests:
    """Comprehensive test suite using CSV data."""
    
    def __init__(self, csv_file: str = 'sms_backup.csv'):
        self.csv_file = csv_file
        self.parser = SMSParser()
        self.test_results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'detailed_results': [],
            'pattern_coverage': defaultdict(int),
            'field_extraction_stats': defaultdict(int),
            'sender_performance': defaultdict(lambda: {'total': 0, 'success': 0})
        }
    
    def load_financial_sms(self) -> List[Dict[str, Any]]:
        """Load financial SMS messages from CSV."""
        financial_messages = []
        
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                for row in reader:
                    text = row.get('text', '').strip()
                    sender = row.get('senderAddress', '').strip()
                    
                    # Filter for financial SMS
                    if self._is_financial_sms(text, sender):
                        financial_messages.append({
                            'text': text,
                            'sender': sender,
                            'timestamp': row.get('updateAt', ''),
                            'id': row.get('id', '')
                        })
        
        except Exception as e:
            print(f"Error loading CSV: {e}")
            return []
        
        return financial_messages
    
    def _is_financial_sms(self, text: str, sender: str) -> bool:
        """Determine if SMS is financial."""
        financial_senders = [
            'SBIUPI', 'SBICRD', 'HDFCUPI', 'HDFCBN', 'AXISBK', 'ICICIUPI',
            'RBLBNK', 'ATMSBI', 'CREDIN', 'GROWWZ'
        ]
        
        # Check sender patterns
        for fs in financial_senders:
            if fs in sender.upper():
                return True
        
        # Check content patterns
        financial_keywords = [
            'debited', 'credited', 'UPI', 'transaction', 'payment', 'balance',
            'Rs', '₹', 'INR', 'refno', 'EMI', 'loan', 'card'
        ]
        
        text_lower = text.lower()
        return any(keyword.lower() in text_lower for keyword in financial_keywords)
    
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run comprehensive tests on all financial SMS."""
        print("🔍 Loading financial SMS messages...")
        financial_sms = self.load_financial_sms()
        
        print(f"📊 Found {len(financial_sms)} financial SMS messages")
        
        # Group by sender for analysis
        sender_groups = defaultdict(list)
        for sms in financial_sms:
            sender_groups[sms['sender']].append(sms)
        
        print(f"📱 Testing messages from {len(sender_groups)} different senders")
        
        self.test_results['total_tests'] = len(financial_sms)
        
        # Test each message
        for i, sms_data in enumerate(financial_sms):
            await self._test_single_sms(sms_data, i)
            
            # Progress indicator
            if (i + 1) % 100 == 0:
                print(f"Processed {i + 1}/{len(financial_sms)} messages...")
        
        # Calculate final statistics
        self._calculate_final_stats()
        
        return self.test_results
    
    async def _test_single_sms(self, sms_data: Dict[str, Any], index: int):
        """Test a single SMS message."""
        text = sms_data['text']
        sender = sms_data['sender']
        
        try:
            # Parse the SMS
            results = await self.parser.parse_sms(text)
            
            # Update sender performance
            self.test_results['sender_performance'][sender]['total'] += 1
            
            if results:
                self.test_results['passed'] += 1
                self.test_results['sender_performance'][sender]['success'] += 1
                
                # Analyze patterns and fields
                for result in results:
                    sms_type = result.get('sms_type', 'Unknown')
                    subtype = result.get('sms_event_subtype', 'Unknown')
                    self.test_results['pattern_coverage'][f"{sms_type}:{subtype}"] += 1
                    
                    # Count extracted fields
                    for field, value in result.items():
                        if value is not None and field not in ['sms_type', 'sms_event_subtype', 'sms_info_type']:
                            self.test_results['field_extraction_stats'][field] += 1
                
                # Store successful result
                self.test_results['detailed_results'].append({
                    'index': index,
                    'sender': sender,
                    'text_preview': text[:100] + '...' if len(text) > 100 else text,
                    'status': 'SUCCESS',
                    'results_count': len(results),
                    'extracted_fields': list(results[0].keys()) if results else [],
                    'classification': f"{results[0].get('sms_type', 'Unknown')}:{results[0].get('sms_event_subtype', 'Unknown')}" if results else 'Unknown'
                })
            else:
                self.test_results['failed'] += 1
                self.test_results['detailed_results'].append({
                    'index': index,
                    'sender': sender,
                    'text_preview': text[:100] + '...' if len(text) > 100 else text,
                    'status': 'FAILED',
                    'error': 'No structured data extracted'
                })
        
        except Exception as e:
            self.test_results['failed'] += 1
            self.test_results['detailed_results'].append({
                'index': index,
                'sender': sender,
                'text_preview': text[:100] + '...' if len(text) > 100 else text,
                'status': 'ERROR',
                'error': str(e)
            })
    
    def _calculate_final_stats(self):
        """Calculate final statistics."""
        total = self.test_results['total_tests']
        passed = self.test_results['passed']
        
        self.test_results['success_rate'] = (passed / total * 100) if total > 0 else 0
        
        # Calculate sender success rates
        for sender, stats in self.test_results['sender_performance'].items():
            if stats['total'] > 0:
                stats['success_rate'] = (stats['success'] / stats['total'] * 100)
    
    def generate_test_report(self) -> str:
        """Generate a comprehensive test report."""
        results = self.test_results
        
        report = []
        report.append("=" * 80)
        report.append("📊 COMPREHENSIVE SMS PARSER TEST REPORT")
        report.append("=" * 80)
        
        # Overall statistics
        report.append(f"\n🎯 OVERALL RESULTS:")
        report.append(f"  Total SMS tested: {results['total_tests']}")
        report.append(f"  Successfully parsed: {results['passed']}")
        report.append(f"  Failed to parse: {results['failed']}")
        report.append(f"  Success rate: {results['success_rate']:.1f}%")
        
        # Pattern coverage
        report.append(f"\n🏷️ PATTERN COVERAGE:")
        pattern_counts = Counter(results['pattern_coverage'])
        for pattern, count in pattern_counts.most_common(10):
            report.append(f"  {pattern}: {count} messages")
        
        # Field extraction statistics
        report.append(f"\n🔍 FIELD EXTRACTION STATISTICS:")
        field_counts = Counter(results['field_extraction_stats'])
        for field, count in field_counts.most_common(15):
            report.append(f"  {field}: {count} extractions")
        
        # Sender performance
        report.append(f"\n📱 SENDER PERFORMANCE (Top 15):")
        sender_performance = []
        for sender, stats in results['sender_performance'].items():
            if stats['total'] >= 5:  # Only show senders with 5+ messages
                sender_performance.append((sender, stats['success_rate'], stats['total'], stats['success']))
        
        sender_performance.sort(key=lambda x: x[1], reverse=True)
        for sender, success_rate, total, success in sender_performance[:15]:
            report.append(f"  {sender}: {success_rate:.1f}% ({success}/{total})")
        
        # Failed cases analysis
        failed_cases = [r for r in results['detailed_results'] if r['status'] != 'SUCCESS']
        if failed_cases:
            report.append(f"\n❌ FAILED CASES ANALYSIS:")
            report.append(f"  Total failed: {len(failed_cases)}")
            
            # Group by sender
            failed_by_sender = defaultdict(int)
            for case in failed_cases:
                failed_by_sender[case['sender']] += 1
            
            report.append(f"  Top failing senders:")
            for sender, count in Counter(failed_by_sender).most_common(10):
                report.append(f"    {sender}: {count} failures")
        
        report.append("\n" + "=" * 80)
        
        return "\n".join(report)
    
    def save_detailed_results(self, filename: str = 'comprehensive_test_results.json'):
        """Save detailed results to JSON file."""
        with open(filename, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        print(f"💾 Detailed results saved to {filename}")


async def main():
    """Main function to run comprehensive tests."""
    print("🚀 Starting Comprehensive SMS Parser Tests")
    print("Using real financial SMS data from CSV file")
    
    tester = ComprehensiveCSVTests()
    
    # Run comprehensive tests
    results = await tester.run_comprehensive_tests()
    
    # Generate and display report
    report = tester.generate_test_report()
    print(report)
    
    # Save detailed results
    tester.save_detailed_results()
    
    # Test specific challenging cases
    print("\n🎯 Testing specific challenging cases...")
    await test_challenging_cases()


async def test_challenging_cases():
    """Test specific challenging SMS patterns."""
    parser = SMSParser()
    
    challenging_cases = [
        # Real SMS from CSV with various patterns
        "Dear UPI user A/C X4884 debited by 2000.0 on date 30Apr24 trf to SMAAASH Refno ************. If not u? call **********. -SBI",
        "Cash payment of Rs.2670 received on 30-04-2024 for Loan/Card (XX1772) Ref: ********************, will reflect in your account in 2 working days-RBL Bank",
        "Dear Customer, transaction number ************ for Rs.6026.00 by SBI Debit Card X3804 done at ******** on 01May24 at 22:01:31. Your updated available balance is Rs.32925.97.",
        "Dear SBI UPI User, ur A/cX4884 credited by Rs6000 on 01May24 by  (Ref no ************)",
        "Your RBL Bank Loan A/C ****1772 has an overdue amt of Rs. 2661 for the past 24 days. We request you to clear the total overdue amount at the earliest.",
        "Dear Cardholder, Your EMI Booking amount of Rs.36866.65 at VIVO Mobiles has been converted to 6 EMIs at 15 percent reducing rate of interest and Processing Fee of Rs.99 on 01/05/2024.",
        "We have received payment of Rs.13,293.00 via BBPS & the same has been credited to your SBI Credit Card. Your available limit is Rs.68,963.54.",
    ]
    
    for i, sms in enumerate(challenging_cases, 1):
        print(f"\n🧪 Test Case {i}:")
        print(f"SMS: {sms[:80]}...")
        
        try:
            results = await parser.parse_sms(sms)
            if results:
                print(f"✅ Successfully parsed {len(results)} event(s)")
                for j, result in enumerate(results, 1):
                    print(f"  Event {j}: {result.get('sms_type')} - {result.get('sms_event_subtype')}")
                    if result.get('amount'):
                        print(f"    Amount: {result.get('amount')}")
                    if result.get('account_number'):
                        print(f"    Account: {result.get('account_number')}")
            else:
                print("❌ Failed to parse")
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
