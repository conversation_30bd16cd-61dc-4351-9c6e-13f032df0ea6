#!/usr/bin/env python3
"""
Final comprehensive test suite for SMS Parser.
Tests all major patterns found in real SMS data with expected outputs.
"""

import asyncio
import json
from typing import List, Dict, Any, Tuple
from sms_parser import SMSParser, parse_sms_sync


class FinalTestSuite:
    """Final comprehensive test suite with expected outputs."""
    
    def __init__(self):
        self.parser = SMSParser()
        self.test_cases = self._create_test_cases()
    
    def _create_test_cases(self) -> List[Dict[str, Any]]:
        """Create comprehensive test cases based on real SMS patterns."""
        return [
            # UPI Transactions
            {
                'name': 'SBI UPI Debit - Standard Format',
                'input': 'Dear UPI user A/C X4884 debited by 2000.0 on date 30Apr24 trf to SMAAASH Refno ************. If not u? call **********. -SBI',
                'expected': {
                    'sms_type': 'Purchase',
                    'sms_event_subtype': 'UPI',
                    'sms_info_type': 'Outflow',
                    'amount': '2000',
                    'account_number': 'X4884',
                    'upi_recipient': 'SMAAASH',
                    'txn_ref': '************'
                }
            },
            {
                'name': 'SBI UPI Credit - Standard Format',
                'input': 'Dear SBI UPI User, ur A/cX4884 credited by Rs6000 on 01May24 by  (Ref no ************)',
                'expected': {
                    'sms_type': 'Purchase',
                    'sms_event_subtype': 'UPI',
                    'sms_info_type': 'Inflow',
                    'amount': '6000',
                    'account_number': 'X4884',
                    'txn_ref': '************'
                }
            },
            {
                'name': 'UPI with Person Name',
                'input': 'Dear UPI user A/C X4884 debited by 180.0 on date 02May24 trf to Dheeraj Neeraj J Refno ************. If not u? call **********. -SBI',
                'expected': {
                    'sms_type': 'Purchase',
                    'sms_event_subtype': 'UPI',
                    'sms_info_type': 'Outflow',
                    'amount': '180',
                    'account_number': 'X4884',
                    'upi_recipient': 'Dheeraj Neeraj J',
                    'txn_ref': '************'
                }
            },
            
            # Debit Card Transactions
            {
                'name': 'SBI Debit Card Transaction',
                'input': 'Dear Customer, transaction number ************ for Rs.6026.00 by SBI Debit Card X3804 done at ******** on 01May24 at 22:01:31. Your updated available balance is Rs.32925.97.',
                'expected': {
                    'sms_type': 'Purchase',
                    'sms_event_subtype': 'Debit Card',
                    'sms_info_type': 'Outflow',
                    'amount': '6026',
                    'card_number': 'X3804',
                    'txn_ref': '************'
                }
            },
            {
                'name': 'Credit Card Payment',
                'input': 'We have received payment of Rs.13,293.00 via BBPS & the same has been credited to your SBI Credit Card. Your available limit is Rs.68,963.54.',
                'expected': {
                    'sms_type': 'Purchase',
                    'sms_event_subtype': 'Debit Card',
                    'sms_info_type': 'Inflow',
                    'amount': '13293'
                }
            },
            
            # EMI and Loan Transactions
            {
                'name': 'EMI Conversion',
                'input': 'Dear Cardholder, Your EMI Booking amount of Rs.36866.65 at VIVO Mobiles has been converted to 6 EMIs at 15 percent reducing rate of interest and Processing Fee of Rs.99 on 01/05/2024.',
                'expected': {
                    'sms_type': 'Payment',
                    'sms_event_subtype': 'EMI Payment',
                    'sms_info_type': 'Outflow',
                    'emi_amount': '36866.65',
                    'merchant_name': 'VIVO Mobiles'
                }
            },
            {
                'name': 'Loan Overdue',
                'input': 'Your RBL Bank Loan A/C ****1772 has an overdue amt of Rs. 2661 for the past 24 days. We request you to clear the total overdue amount at the earliest.',
                'expected': {
                    'sms_type': 'Accounts',
                    'sms_event_subtype': 'Loan',
                    'sms_info_type': 'Account Status',
                    'account_number': '****1772',
                    'current_amount': '2661',
                    'default_status': 'overdue'
                }
            },
            {
                'name': 'Cash Payment for Loan',
                'input': 'Cash payment of Rs.2670 received on 30-04-2024 for Loan/Card (XX1772) Ref: ********************, will reflect in your account in 2 working days-RBL Bank',
                'expected': {
                    'sms_type': 'Purchase',
                    'sms_event_subtype': 'Debit Card',
                    'sms_info_type': 'Inflow',
                    'amount': '2670',
                    'account_number': 'XX1772',
                    'txn_ref': '********************'
                }
            },
            
            # Balance and Account Updates
            {
                'name': 'Balance Update with Transaction',
                'input': 'UPI txn of Rs 180.0 debited from A/c X4884 on 02May24 to Dheeraj Neeraj J. Ref no ************. Avl bal Rs 2000.',
                'expected': [
                    {
                        'sms_type': 'Purchase',
                        'sms_event_subtype': 'UPI',
                        'sms_info_type': 'Outflow',
                        'amount': '180',
                        'account_number': 'X4884',
                        'upi_recipient': 'Dheeraj Neeraj J',
                        'txn_ref': '************'
                    },
                    {
                        'sms_type': 'Accounts',
                        'sms_event_subtype': 'Bank Account',
                        'sms_info_type': 'Account Status',
                        'current_amount': '2000'
                    }
                ]
            },
            
            # Salary Credits
            {
                'name': 'Salary Credit',
                'input': 'Dear SBI User, your A/c X4884-credited by Rs.500 on 12Mar25 transfer from Tanya  Tomar Ref No ************ -SBI',
                'expected': {
                    'sms_type': 'Deposit & Withdrawal',
                    'sms_event_subtype': 'Monthly Salary Credit',
                    'sms_info_type': 'Inflow',
                    'amount': '500',
                    'account_number': 'X4884',
                    'employer_name': 'Tanya  Tomar',
                    'txn_ref': '************'
                }
            },
            
            # Investment Transactions
            {
                'name': 'Investment Transaction',
                'input': 'SIP of Rs 5,000 invested in HDFC Equity Fund on 01-06-2024. Folio: 12345/67. Units: 125.50. NAV: Rs 39.84. Ref: SIP240601',
                'expected': {
                    'sms_type': 'Investment',
                    'sms_event_subtype': 'Investment',
                    'sms_info_type': 'Other',
                    'amount': '5000',
                    'bank_name': 'HDFC'
                }
            },
            
            # Edge Cases
            {
                'name': 'Multiple Amounts in SMS',
                'input': 'EMI of Rs 5,500 for Loan A/c ********* paid on 01Jun24. EMI No: 12/36. Interest: Rs 2,100. Late fee: Rs 0. Ref: EMI789012',
                'expected': {
                    'sms_type': 'Payment',
                    'sms_event_subtype': 'EMI Payment',
                    'sms_info_type': 'Outflow',
                    'emi_amount': '5500',
                    'account_number': '123456',
                    'interest_charged': '2100',
                    'late_fee_charged': '0',
                    'txn_ref': 'EMI789012'
                }
            },
            {
                'name': 'Different Date Formats',
                'input': 'Transaction of Rs.119.00 at SPOTIFY SI on 02-06-25. Card ending 4465. Ref: SPOT123456',
                'expected': {
                    'sms_type': 'Purchase',
                    'sms_event_subtype': 'Debit Card',
                    'sms_info_type': 'Outflow',
                    'amount': '119',
                    'merchant_name': 'SPOTIFY SI',
                    'card_number': '4465'
                }
            },
            {
                'name': 'NEFT Credit',
                'input': 'Dear Customer, INR 1,494.23 credited to your A/c No XX4884 on 03/06/2024 through NEFT with UTR CITIN24477998164 by PAYPAL PAYMENTS PL-OPGSP COLL AC',
                'expected': {
                    'sms_type': 'Deposit & Withdrawal',
                    'sms_event_subtype': 'Monthly Salary Credit',
                    'sms_info_type': 'Inflow',
                    'amount': '1494.23',
                    'account_number': 'XX4884',
                    'employer_name': 'PAYPAL PAYMENTS PL-OPGSP COLL AC',
                    'txn_ref': 'CITIN24477998164'
                }
            }
        ]
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all test cases and return results."""
        results = {
            'total_tests': len(self.test_cases),
            'passed': 0,
            'failed': 0,
            'test_results': []
        }
        
        print(f"🧪 Running {len(self.test_cases)} comprehensive test cases...")
        
        for i, test_case in enumerate(self.test_cases):
            result = await self._run_single_test(test_case, i + 1)
            results['test_results'].append(result)
            
            if result['status'] == 'PASS':
                results['passed'] += 1
                print(f"✅ {result['name']}")
            else:
                results['failed'] += 1
                print(f"❌ {result['name']}: {result['error']}")
        
        results['success_rate'] = (results['passed'] / results['total_tests'] * 100)
        
        return results
    
    async def _run_single_test(self, test_case: Dict[str, Any], test_num: int) -> Dict[str, Any]:
        """Run a single test case."""
        try:
            parsed_results = await self.parser.parse_sms(test_case['input'])
            expected = test_case['expected']
            
            if isinstance(expected, list):
                # Multiple expected results
                if len(parsed_results) != len(expected):
                    return {
                        'test_number': test_num,
                        'name': test_case['name'],
                        'status': 'FAIL',
                        'error': f"Expected {len(expected)} results, got {len(parsed_results)}",
                        'expected': expected,
                        'actual': parsed_results
                    }
                
                # Check each result
                for i, (actual, exp) in enumerate(zip(parsed_results, expected)):
                    validation_error = self._validate_result(actual, exp)
                    if validation_error:
                        return {
                            'test_number': test_num,
                            'name': test_case['name'],
                            'status': 'FAIL',
                            'error': f"Result {i+1}: {validation_error}",
                            'expected': expected,
                            'actual': parsed_results
                        }
            else:
                # Single expected result
                if not parsed_results:
                    return {
                        'test_number': test_num,
                        'name': test_case['name'],
                        'status': 'FAIL',
                        'error': 'No results parsed',
                        'expected': expected,
                        'actual': parsed_results
                    }
                
                # Validate the first result
                validation_error = self._validate_result(parsed_results[0], expected)
                if validation_error:
                    return {
                        'test_number': test_num,
                        'name': test_case['name'],
                        'status': 'FAIL',
                        'error': validation_error,
                        'expected': expected,
                        'actual': parsed_results[0]
                    }
            
            return {
                'test_number': test_num,
                'name': test_case['name'],
                'status': 'PASS',
                'expected': expected,
                'actual': parsed_results
            }
        
        except Exception as e:
            return {
                'test_number': test_num,
                'name': test_case['name'],
                'status': 'ERROR',
                'error': str(e),
                'expected': test_case['expected'],
                'actual': None
            }
    
    def _validate_result(self, actual: Dict[str, Any], expected: Dict[str, Any]) -> str:
        """Validate actual result against expected result."""
        for key, expected_value in expected.items():
            actual_value = actual.get(key)
            
            if actual_value is None:
                return f"Missing field '{key}'"
            
            # Normalize values for comparison
            if isinstance(expected_value, str) and isinstance(actual_value, str):
                if expected_value.lower() != actual_value.lower():
                    return f"Field '{key}': expected '{expected_value}', got '{actual_value}'"
            elif expected_value != actual_value:
                return f"Field '{key}': expected '{expected_value}', got '{actual_value}'"
        
        return None  # No validation errors
    
    def generate_final_report(self, results: Dict[str, Any]) -> str:
        """Generate final test report."""
        report = []
        report.append("=" * 80)
        report.append("🎯 FINAL SMS PARSER TEST REPORT")
        report.append("=" * 80)
        
        report.append(f"\n📊 OVERALL RESULTS:")
        report.append(f"  Total tests: {results['total_tests']}")
        report.append(f"  Passed: {results['passed']}")
        report.append(f"  Failed: {results['failed']}")
        report.append(f"  Success rate: {results['success_rate']:.1f}%")
        
        # Failed tests details
        failed_tests = [t for t in results['test_results'] if t['status'] != 'PASS']
        if failed_tests:
            report.append(f"\n❌ FAILED TESTS:")
            for test in failed_tests:
                report.append(f"  {test['test_number']}. {test['name']}")
                report.append(f"     Error: {test['error']}")
        
        report.append("\n" + "=" * 80)
        return "\n".join(report)


async def main():
    """Main function to run final test suite."""
    print("🚀 Final SMS Parser Test Suite")
    print("Testing comprehensive patterns with expected outputs")
    
    test_suite = FinalTestSuite()
    results = await test_suite.run_all_tests()
    
    # Generate and display report
    report = test_suite.generate_final_report(results)
    print(f"\n{report}")
    
    # Save detailed results
    with open('final_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to final_test_results.json")


if __name__ == "__main__":
    asyncio.run(main())
