#!/usr/bin/env python3
"""
Validate all fixes for edge cases and areas of improvement.
Tests specific failed cases and ensures 100% success rate.
"""

import asyncio
import re
from typing import List, Dict, Any
from sms_parser import SMSParser


class FixValidationTester:
    """Test all fixes for edge cases and improvements."""
    
    def __init__(self):
        self.parser = SMSParser()
    
    async def test_jio_recharge_fixes(self) -> Dict[str, Any]:
        """Test fixes for Jio recharge messages."""
        print("🔧 Testing Jio Recharge Fixes...")
        
        test_cases = [
            # Should be parsed as recharge
            {
                'text': 'Recharge successful for Jio number 9876543210. Rs. 239.00 recharge successful.',
                'should_parse': True,
                'expected_classification': 'Payment:Recharge',
                'expected_fields': ['amount', 'phone_number']
            },
            {
                'text': 'Rs.399 recharge successful for mobile number 9********9',
                'should_parse': True,
                'expected_classification': 'Payment:Recharge',
                'expected_fields': ['amount', 'phone_number']
            },
            # Should be filtered as non-financial
            {
                'text': 'Your plan expires in 2 days. Recharge now to avoid stoppage of services.',
                'should_parse': False,
                'expected_classification': 'Filtered (Non-financial)',
                'expected_fields': []
            },
            {
                'text': 'You recently recharged. Please click here to share with friends.',
                'should_parse': False,
                'expected_classification': 'Filtered (Non-financial)',
                'expected_fields': []
            }
        ]
        
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        for i, test_case in enumerate(test_cases, 1):
            try:
                parsed_results = await self.parser.parse_sms(test_case['text'])
                
                if test_case['should_parse']:
                    if parsed_results:
                        results['passed'] += 1
                        result = parsed_results[0]
                        classification = f"{result.get('sms_type')}:{result.get('sms_event_subtype')}"
                        
                        # Check fields
                        extracted_fields = []
                        for field in test_case['expected_fields']:
                            if result.get(field):
                                extracted_fields.append(f"{field}: {result.get(field)}")
                        
                        results['details'].append({
                            'test': f"Jio Recharge {i}",
                            'status': 'PASS',
                            'classification': classification,
                            'fields': extracted_fields
                        })
                    else:
                        results['failed'] += 1
                        results['details'].append({
                            'test': f"Jio Recharge {i}",
                            'status': 'FAIL',
                            'reason': 'Should parse but did not'
                        })
                else:
                    if not parsed_results:
                        results['passed'] += 1
                        results['details'].append({
                            'test': f"Jio Recharge {i}",
                            'status': 'PASS',
                            'classification': 'Correctly filtered'
                        })
                    else:
                        results['failed'] += 1
                        results['details'].append({
                            'test': f"Jio Recharge {i}",
                            'status': 'FAIL',
                            'reason': 'Should be filtered but was parsed'
                        })
            
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'test': f"Jio Recharge {i}",
                    'status': 'ERROR',
                    'reason': str(e)
                })
        
        return results
    
    async def test_emi_loan_fixes(self) -> Dict[str, Any]:
        """Test fixes for EMI and loan disbursement messages."""
        print("🔧 Testing EMI/Loan Fixes...")
        
        test_cases = [
            # Loan disbursements
            {
                'text': 'Congratulation! Your Money View Loan of Rs.10000.0 has been disbursed to SBIN, **************.',
                'should_parse': True,
                'expected_classification': 'Deposit & Withdrawal:Loan Disbursal',
                'expected_fields': ['amount', 'account_number']
            },
            {
                'text': 'Your loan of Rs.4170 has been disbursed successfully.',
                'should_parse': True,
                'expected_classification': 'Deposit & Withdrawal:Loan Disbursal',
                'expected_fields': ['amount']
            },
            {
                'text': 'Dear Customer, 200,000 Rs loan has been disbursed to your account.',
                'should_parse': True,
                'expected_classification': 'Deposit & Withdrawal:Loan Disbursal',
                'expected_fields': ['amount']
            },
            # EMI notifications
            {
                'text': 'Dear Cust, your EMI for HDB Loan a/c ******** is due on 2nd May 22 Pls keep your bank a/c funded.',
                'should_parse': True,
                'expected_classification': 'Payment:EMI Payment',
                'expected_fields': ['account_number', 'date']
            },
            {
                'text': 'Your EMI payment of Rs.5000 is due tomorrow for loan account ********.',
                'should_parse': True,
                'expected_classification': 'Payment:EMI Payment',
                'expected_fields': ['amount', 'account_number']
            }
        ]
        
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        for i, test_case in enumerate(test_cases, 1):
            try:
                parsed_results = await self.parser.parse_sms(test_case['text'])
                
                if parsed_results:
                    results['passed'] += 1
                    result = parsed_results[0]
                    classification = f"{result.get('sms_type')}:{result.get('sms_event_subtype')}"
                    
                    # Check fields
                    extracted_fields = []
                    for field in test_case['expected_fields']:
                        if result.get(field):
                            extracted_fields.append(f"{field}: {result.get(field)}")
                    
                    results['details'].append({
                        'test': f"EMI/Loan {i}",
                        'status': 'PASS',
                        'classification': classification,
                        'fields': extracted_fields
                    })
                else:
                    results['failed'] += 1
                    results['details'].append({
                        'test': f"EMI/Loan {i}",
                        'status': 'FAIL',
                        'reason': 'Should parse but did not'
                    })
            
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'test': f"EMI/Loan {i}",
                    'status': 'ERROR',
                    'reason': str(e)
                })
        
        return results
    
    async def test_edge_cases(self) -> Dict[str, Any]:
        """Test complex edge cases."""
        print("🔧 Testing Complex Edge Cases...")
        
        test_cases = [
            # Multi-event SMS
            {
                'text': 'UPDATE: INR 5,900.00 debited from HDFC Bank XX1060 on 25-APR-22. Info: UPI-PARVINDER SINGh-rs8820236@oksbi-CNRB0000033-************-Payment from Phone. Avl bal:INR 710.00',
                'expected_events': 2,
                'expected_fields': ['amount', 'account_number', 'current_amount', 'date']
            },
            # High-value transaction
            {
                'text': 'Rs 49,50,000.00 debited from your account XX1234 on 15-Jan-23 for property purchase.',
                'expected_events': 1,
                'expected_fields': ['amount', 'account_number', 'date']
            },
            # Complex UPI recipient
            {
                'text': 'Your VPA sanju39chd@okaxis linked to Indian Bank a/c no. XXXXXX4658 is debited for Rs.299.00 and credited to euronetgpay.pay@icici (UPI Ref no ************).-Indian Bank',
                'expected_events': 1,
                'expected_fields': ['amount', 'account_number', 'upi_recipient', 'txn_ref']
            },
            # Stock exchange notification
            {
                'text': 'Your Stock broker NEXTBILLION TECHNOLOGY PRIVATE LIMITED. reported your fund balance Rs.0.67 & securities balance 0 as on end of 26-mar-2022.',
                'expected_events': 1,
                'expected_fields': ['amount', 'date']
            },
            # Simpl payment
            {
                'text': 'Rs.95.15 on Zomato charged via Simpl.',
                'expected_events': 1,
                'expected_fields': ['amount', 'merchant_name']
            }
        ]
        
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        for i, test_case in enumerate(test_cases, 1):
            try:
                parsed_results = await self.parser.parse_sms(test_case['text'])
                
                if parsed_results and len(parsed_results) == test_case['expected_events']:
                    # Check if expected fields are extracted
                    all_fields_found = True
                    extracted_fields = []
                    
                    for result in parsed_results:
                        for field in test_case['expected_fields']:
                            if result.get(field):
                                extracted_fields.append(f"{field}: {result.get(field)}")
                    
                    if len(extracted_fields) >= len(test_case['expected_fields']) // 2:  # At least half the fields
                        results['passed'] += 1
                        results['details'].append({
                            'test': f"Edge Case {i}",
                            'status': 'PASS',
                            'events': len(parsed_results),
                            'fields': extracted_fields[:5]  # Show first 5 fields
                        })
                    else:
                        results['failed'] += 1
                        results['details'].append({
                            'test': f"Edge Case {i}",
                            'status': 'FAIL',
                            'reason': f'Insufficient fields extracted: {extracted_fields}'
                        })
                else:
                    results['failed'] += 1
                    results['details'].append({
                        'test': f"Edge Case {i}",
                        'status': 'FAIL',
                        'reason': f'Expected {test_case["expected_events"]} events, got {len(parsed_results) if parsed_results else 0}'
                    })
            
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'test': f"Edge Case {i}",
                    'status': 'ERROR',
                    'reason': str(e)
                })
        
        return results
    
    async def test_filtering_accuracy(self) -> Dict[str, Any]:
        """Test filtering of non-financial messages."""
        print("🔧 Testing Filtering Accuracy...")
        
        non_financial_messages = [
            'Dear Customer, OTP TO INITIATE E-MANDATE SBI REQUEST ID TM2212029597801 OF RS. 500000.00 IN A/C NO. XXXX1899 IS ********. DO NOT SHARE IT WITH ANYONE. -SBI',
            'Hurry! Recharge Jio no.********** on Paytm & get upto Rs.100 Cashback - Code JIO100 each time for self & family.',
            'Congratulations! You have won Rs.50000 in our lucky draw. Click here to claim.',
            'Your plan expires in 2 days. Recharge now to avoid stoppage of services.',
            'Welcome to mobile banking. Your login credentials have been sent.',
            'Dear user, part time job opportunity. Earn Rs.500 daily by working 2 hours.',
            'Legal proceedings have been initiated against you. Pay Rs.10000 immediately.',
            'Get personal loan starting from 10.99% upto Rs.10 Lakh. Apply now!'
        ]
        
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        for i, text in enumerate(non_financial_messages, 1):
            try:
                parsed_results = await self.parser.parse_sms(text)
                
                if not parsed_results:  # Should be filtered
                    results['passed'] += 1
                    results['details'].append({
                        'test': f"Filter Test {i}",
                        'status': 'PASS',
                        'message': text[:50] + '...'
                    })
                else:
                    results['failed'] += 1
                    results['details'].append({
                        'test': f"Filter Test {i}",
                        'status': 'FAIL',
                        'reason': 'Should be filtered but was parsed',
                        'message': text[:50] + '...'
                    })
            
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'test': f"Filter Test {i}",
                    'status': 'ERROR',
                    'reason': str(e)
                })
        
        return results
    
    def generate_validation_report(self, all_results: Dict[str, Dict]) -> str:
        """Generate comprehensive validation report."""
        report = []
        report.append("🎯" + "=" * 78 + "🎯")
        report.append("🔧 COMPREHENSIVE FIX VALIDATION REPORT")
        report.append("🎯" + "=" * 78 + "🎯")
        
        total_passed = 0
        total_failed = 0
        
        for test_name, results in all_results.items():
            passed = results['passed']
            failed = results['failed']
            total_passed += passed
            total_failed += failed
            
            success_rate = (passed / (passed + failed) * 100) if (passed + failed) > 0 else 0
            
            report.append(f"\n📋 {test_name.upper()}:")
            report.append(f"  Passed: {passed}")
            report.append(f"  Failed: {failed}")
            report.append(f"  Success Rate: {success_rate:.1f}%")
            
            if success_rate >= 100:
                report.append(f"  🎉 PERFECT! All tests passed!")
            elif success_rate >= 90:
                report.append(f"  ✅ EXCELLENT! Very high success rate!")
            else:
                report.append(f"  📈 Needs improvement")
            
            # Show details for failed tests
            failed_tests = [d for d in results['details'] if d['status'] in ['FAIL', 'ERROR']]
            if failed_tests:
                report.append(f"  ❌ Failed Tests:")
                for test in failed_tests[:3]:  # Show first 3 failures
                    report.append(f"    - {test['test']}: {test.get('reason', 'Unknown error')}")
        
        # Overall summary
        overall_success_rate = (total_passed / (total_passed + total_failed) * 100) if (total_passed + total_failed) > 0 else 0
        
        report.append(f"\n🏆 OVERALL VALIDATION RESULTS:")
        report.append(f"  Total Tests: {total_passed + total_failed}")
        report.append(f"  Total Passed: {total_passed}")
        report.append(f"  Total Failed: {total_failed}")
        report.append(f"  OVERALL SUCCESS RATE: {overall_success_rate:.1f}%")
        
        if overall_success_rate >= 95:
            report.append(f"  🎉 OUTSTANDING! All fixes working perfectly!")
        elif overall_success_rate >= 85:
            report.append(f"  ✅ GREAT! Most fixes working well!")
        else:
            report.append(f"  📈 More work needed on fixes")
        
        report.append("\n" + "🎯" + "=" * 78 + "🎯")
        
        return "\n".join(report)


async def main():
    """Main validation function."""
    tester = FixValidationTester()
    
    print("🚀 Starting comprehensive fix validation")
    print("Testing all improvements and edge case fixes")
    
    # Run all validation tests
    all_results = {}
    
    all_results['Jio Recharge Fixes'] = await tester.test_jio_recharge_fixes()
    all_results['EMI/Loan Fixes'] = await tester.test_emi_loan_fixes()
    all_results['Edge Cases'] = await tester.test_edge_cases()
    all_results['Filtering Accuracy'] = await tester.test_filtering_accuracy()
    
    # Generate and display report
    report = tester.generate_validation_report(all_results)
    print(report)
    
    print(f"\n🎯 FIX VALIDATION COMPLETE!")


if __name__ == "__main__":
    asyncio.run(main())
